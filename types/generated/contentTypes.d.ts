import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesRelease extends Schema.CollectionType {
  collectionName: 'strapi_releases';
  info: {
    singularName: 'release';
    pluralName: 'releases';
    displayName: 'Release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    releasedAt: Attribute.DateTime;
    scheduledAt: Attribute.DateTime;
    timezone: Attribute.String;
    status: Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Attribute.Required;
    actions: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Schema.CollectionType {
  collectionName: 'strapi_release_actions';
  info: {
    singularName: 'release-action';
    pluralName: 'release-actions';
    displayName: 'Release Action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    type: Attribute.Enumeration<['publish', 'unpublish']> & Attribute.Required;
    entry: Attribute.Relation<
      'plugin::content-releases.release-action',
      'morphToOne'
    >;
    contentType: Attribute.String & Attribute.Required;
    locale: Attribute.String;
    release: Attribute.Relation<
      'plugin::content-releases.release-action',
      'manyToOne',
      'plugin::content-releases.release'
    >;
    isEntryValid: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::content-releases.release-action',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 50;
        },
        number
      >;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    insurance_histories: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToMany',
      'api::insurance-history.insurance-history'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAppConfigAppConfig extends Schema.SingleType {
  collectionName: 'app_configs';
  info: {
    singularName: 'app-config';
    pluralName: 'app-configs';
    displayName: 'App Config';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    bg_home_header: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    page_content_open_bank_card: Attribute.RichText &
      Attribute.Required &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    bg_header_user_point: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    bg_header_user_wallet: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    content_guide_upload_image_bhvcxauto: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    content_guide_payment: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    content_rule_lucky_wheel: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::app-config.app-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::app-config.app-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBaoHiemBaoHiem extends Schema.CollectionType {
  collectionName: 'bao_hiems';
  info: {
    singularName: 'bao-hiem';
    pluralName: 'bao-hiems';
    displayName: 'B\u1EA3o hi\u1EC3m';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    des: Attribute.String;
    images: Attribute.Media<'images' | 'files' | 'videos' | 'audios', true>;
    price: Attribute.Integer;
    discount: Attribute.Float;
    loai_bao_hiems: Attribute.Relation<
      'api::bao-hiem.bao-hiem',
      'manyToMany',
      'api::loai-bao-hiem.loai-bao-hiem'
    >;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    term: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    type: Attribute.Enumeration<['BHTNDSCAR', 'BHTNDSBIKE', 'BHVCCAR']> &
      Attribute.Required;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bao-hiem.bao-hiem',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bao-hiem.bao-hiem',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCarCar extends Schema.CollectionType {
  collectionName: 'cars';
  info: {
    singularName: 'car';
    pluralName: 'cars';
    displayName: 'Xe';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    bsx: Attribute.String;
    car_categories: Attribute.Relation<
      'api::car.car',
      'oneToOne',
      'api::loai-xe.loai-xe'
    >;
    car_brands: Attribute.Relation<
      'api::car.car',
      'oneToOne',
      'api::car-type.car-type'
    >;
    sokhung: Attribute.String;
    somay: Attribute.String;
    gpsId: Attribute.String;
    car_line: Attribute.Relation<
      'api::car.car',
      'oneToOne',
      'api::car-category.car-category'
    >;
    user_id: Attribute.String & Attribute.Required;
    year: Attribute.Integer & Attribute.Required;
    car_histories: Attribute.Relation<
      'api::car.car',
      'oneToMany',
      'api::car-history.car-history'
    >;
    insurance_histories: Attribute.Relation<
      'api::car.car',
      'oneToMany',
      'api::insurance-history.insurance-history'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::car.car', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::car.car', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiCarCategoryCarCategory extends Schema.CollectionType {
  collectionName: 'car_categories';
  info: {
    singularName: 'car-category';
    pluralName: 'car-categories';
    displayName: 'D\u00F2ng xe';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    car_brands: Attribute.Relation<
      'api::car-category.car-category',
      'oneToOne',
      'api::car-type.car-type'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::car-category.car-category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::car-category.car-category',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCarHistoryCarHistory extends Schema.CollectionType {
  collectionName: 'car_histories';
  info: {
    singularName: 'car-history';
    pluralName: 'car-histories';
    displayName: 'Car History';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String & Attribute.Required;
    dateTime: Attribute.DateTime & Attribute.Required;
    car: Attribute.Relation<
      'api::car-history.car-history',
      'manyToOne',
      'api::car.car'
    >;
    image: Attribute.Media<'images'>;
    carId: Attribute.BigInteger & Attribute.Required;
    cost_type: Attribute.Relation<
      'api::car-history.car-history',
      'oneToOne',
      'api::loai-chi-phi.loai-chi-phi'
    >;
    cost_value: Attribute.Integer & Attribute.DefaultTo<0>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::car-history.car-history',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::car-history.car-history',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCarTypeCarType extends Schema.CollectionType {
  collectionName: 'car_types';
  info: {
    singularName: 'car-type';
    pluralName: 'car-types';
    displayName: 'Th\u01B0\u01A1ng hi\u1EC7u';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    description: Attribute.String;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    type: Attribute.Enumeration<['car', 'bike', 'other']> & Attribute.Required;
    order: Attribute.Integer;
    status: Attribute.Integer & Attribute.DefaultTo<1>;
    blocked: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::car-type.car-type',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::car-type.car-type',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCategoryNewsCategoryNews extends Schema.CollectionType {
  collectionName: 'categories_news';
  info: {
    singularName: 'category-news';
    pluralName: 'categories-news';
    displayName: 'Danh m\u1EE5c Tin t\u1EE9c';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    short_description: Attribute.String;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    tin_tuc: Attribute.Relation<
      'api::category-news.category-news',
      'manyToMany',
      'api::news.news'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::category-news.category-news',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::category-news.category-news',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGiftShopDanhMucGiftShopDanhMuc
  extends Schema.CollectionType {
  collectionName: 'gift_shop_danh_mucs';
  info: {
    singularName: 'gift-shop-danh-muc';
    pluralName: 'gift-shop-danh-mucs';
    displayName: 'Gift Shop Danh M\u1EE5c';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    description: Attribute.String;
    gift_shops: Attribute.Relation<
      'api::gift-shop-danh-muc.gift-shop-danh-muc',
      'oneToMany',
      'api::shop.shop'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::gift-shop-danh-muc.gift-shop-danh-muc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::gift-shop-danh-muc.gift-shop-danh-muc',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGridMenuHomeGridMenuHome extends Schema.CollectionType {
  collectionName: 'grid_menu_homes';
  info: {
    singularName: 'grid-menu-home';
    pluralName: 'grid-menu-homes';
    displayName: 'Grid Menu Home';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    icon: Attribute.Media<'images' | 'files' | 'videos' | 'audios'> &
      Attribute.Required;
    screen_nav_to: Attribute.Enumeration<
      [
        'SEARCH',
        'BLOG_STACK',
        'SHOPPING_SCREEN',
        'SCREEN_NAME',
        'SERVICE_CAT_SPA_WASH',
        'SERVICE_CAT_PARKING',
        'SERVICE_CAT_GARA',
        'PRODUCT_CAT',
        'BROWSER_IN_APP'
      ]
    >;
    params: Attribute.JSON;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    order: Attribute.Integer;
    tag: Attribute.Enumeration<['menu', 'hot']> &
      Attribute.Required &
      Attribute.DefaultTo<'menu'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::grid-menu-home.grid-menu-home',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::grid-menu-home.grid-menu-home',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiInsuranceHistoryInsuranceHistory
  extends Schema.CollectionType {
  collectionName: 'insurance_histories';
  info: {
    singularName: 'insurance-history';
    pluralName: 'insurance-histories';
    displayName: '\u0110\u01A1n h\u00E0ng B\u1EA3o Hi\u1EC3m';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    MucDichSD: Attribute.String;
    TenKH: Attribute.String;
    DiaChiKH: Attribute.String;
    TenChuXe: Attribute.String;
    DiaChiChuXe: Attribute.String;
    NgayDau: Attribute.String;
    NgayCuoi: Attribute.String;
    ThamGiaLaiPhu: Attribute.Boolean;
    EmailKH: Attribute.String;
    TrongTai: Attribute.String;
    MTNLaiPhu: Attribute.String;
    SoNguoiToiDa: Attribute.String;
    PhiBHTNDSBB: Attribute.String;
    PhiBHLaiPhu: Attribute.String;
    MayKeo: Attribute.Boolean;
    XeChuyenDung: Attribute.Boolean;
    XeChoTien: Attribute.Boolean;
    XePickUp: Attribute.Boolean;
    XeTaiVan: Attribute.Boolean;
    XeTapLai: Attribute.Boolean;
    XeBus: Attribute.Boolean;
    XeCuuThuong: Attribute.Boolean;
    Xetaxi: Attribute.Boolean;
    XeDauKeo: Attribute.Boolean;
    NamSD: Attribute.String;
    AnBKS: Attribute.Boolean;
    BienKiemSoat: Attribute.String;
    HieuXe: Attribute.String;
    DongXe: Attribute.String;
    NamSX: Attribute.String;
    DienThoai: Attribute.String;
    SoKhung: Attribute.String;
    SoMay: Attribute.String;
    AnPhi: Attribute.Boolean;
    GioDau: Attribute.String;
    GioCuoi: Attribute.String;
    xuatVAT: Attribute.Boolean & Attribute.DefaultTo<false>;
    TenCongTy: Attribute.String;
    DiaChiCongTy: Attribute.String;
    MaSoThue: Attribute.String;
    MaGioiThieu: Attribute.String;
    PaymentId: Attribute.String;
    car: Attribute.Relation<
      'api::insurance-history.insurance-history',
      'manyToOne',
      'api::car.car'
    >;
    user: Attribute.Relation<
      'api::insurance-history.insurance-history',
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    user_id: Attribute.String & Attribute.Required;
    loai_bao_hiem: Attribute.Relation<
      'api::insurance-history.insurance-history',
      'oneToOne',
      'api::loai-bao-hiem.loai-bao-hiem'
    >;
    name: Attribute.String;
    type: Attribute.String;
    hangXe: Attribute.String;
    hieuXe: Attribute.String;
    dongXe: Attribute.String;
    dataRenew: Attribute.JSON & Attribute.Required;
    ProductId: Attribute.String;
    Pr_key: Attribute.String;
    expire_date: Attribute.DateTime;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::insurance-history.insurance-history',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::insurance-history.insurance-history',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLichSuQuayLichSuQuay extends Schema.CollectionType {
  collectionName: 'lich_su_quays';
  info: {
    singularName: 'lich-su-quay';
    pluralName: 'lich-su-quays';
    displayName: 'L\u1ECBch s\u1EED quay';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    user_id: Attribute.String;
    date: Attribute.DateTime;
    result: Attribute.Boolean;
    gift_shop: Attribute.Relation<
      'api::lich-su-quay.lich-su-quay',
      'oneToOne',
      'api::shop.shop'
    >;
    fullName: Attribute.String;
    email: Attribute.String;
    phone: Attribute.String;
    shippingAddress: Attribute.JSON;
    userAvatar: Attribute.String;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    vong_quay_may_man: Attribute.Relation<
      'api::lich-su-quay.lich-su-quay',
      'oneToOne',
      'api::vong-quay-may-man.vong-quay-may-man'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::lich-su-quay.lich-su-quay',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::lich-su-quay.lich-su-quay',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLoaiBaoHiemLoaiBaoHiem extends Schema.CollectionType {
  collectionName: 'loai_bao_hiems';
  info: {
    singularName: 'loai-bao-hiem';
    pluralName: 'loai-bao-hiems';
    displayName: 'Lo\u1EA1i b\u1EA3o hi\u1EC3m';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    des: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    image: Attribute.Media<'images'>;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    bao_hiems: Attribute.Relation<
      'api::loai-bao-hiem.loai-bao-hiem',
      'manyToMany',
      'api::bao-hiem.bao-hiem'
    >;
    hotline: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::loai-bao-hiem.loai-bao-hiem',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::loai-bao-hiem.loai-bao-hiem',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLoaiChiPhiLoaiChiPhi extends Schema.CollectionType {
  collectionName: 'loai_chi_phis';
  info: {
    singularName: 'loai-chi-phi';
    pluralName: 'loai-chi-phis';
    displayName: 'Lo\u1EA1i chi ph\u00ED';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    des: Attribute.Text;
    active: Attribute.Boolean & Attribute.DefaultTo<true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::loai-chi-phi.loai-chi-phi',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::loai-chi-phi.loai-chi-phi',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLoaiXeLoaiXe extends Schema.CollectionType {
  collectionName: 'loai_xes';
  info: {
    singularName: 'loai-xe';
    pluralName: 'loai-xes';
    displayName: 'Lo\u1EA1i xe';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    active: Attribute.Boolean;
    type: Attribute.Enumeration<['car', 'bike', 'other']> &
      Attribute.Required &
      Attribute.DefaultTo<'other'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::loai-xe.loai-xe',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::loai-xe.loai-xe',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiNewsNews extends Schema.CollectionType {
  collectionName: 'list_news';
  info: {
    singularName: 'news';
    pluralName: 'list-news';
    displayName: 'Tin t\u1EE9c';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    short_description: Attribute.String;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    categories: Attribute.Relation<
      'api::news.news',
      'manyToMany',
      'api::category-news.category-news'
    >;
    screen_open: Attribute.Enumeration<
      [
        'LINK',
        'PROMOTION_DETAIL_SCREEN',
        'SHOPPING_STACK',
        'PRODUCT_DETAILS',
        'BLOG_STACK',
        'BLOG_DETAIL_SCREEN',
        'SERVICE_DETAIL',
        'POPUP_SERVICE_OF_BRAND',
        'BHVCCAR',
        'BHTNDSCAR',
        'BHTNDSBIKE'
      ]
    >;
    screen_button_text: Attribute.String;
    screen_param: Attribute.String;
    screen_param_spaId: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::news.news', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::news.news', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiPvcombankLogPvcombankLog extends Schema.CollectionType {
  collectionName: 'pvcombank_logs';
  info: {
    singularName: 'pvcombank-log';
    pluralName: 'pvcombank-logs';
    displayName: 'PVcomBank Acc';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    username: Attribute.String;
    user_id: Attribute.String;
    status: Attribute.String;
    data: Attribute.JSON;
    phone: Attribute.String;
    email: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::pvcombank-log.pvcombank-log',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::pvcombank-log.pvcombank-log',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiShopShop extends Schema.CollectionType {
  collectionName: 'shops';
  info: {
    singularName: 'shop';
    pluralName: 'shops';
    displayName: 'Gift Shop';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    productName: Attribute.String & Attribute.Required;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    point: Attribute.Integer & Attribute.Required & Attribute.DefaultTo<0>;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    gift_shop_danh_muc: Attribute.Relation<
      'api::shop.shop',
      'manyToOne',
      'api::gift-shop-danh-muc.gift-shop-danh-muc'
    >;
    idGiftCode: Attribute.Integer;
    stockLuckyWheel: Attribute.Integer;
    priceLuckyWheel: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::shop.shop', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::shop.shop', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiVongQuayMayManVongQuayMayMan extends Schema.CollectionType {
  collectionName: 'vong_quay_may_men';
  info: {
    singularName: 'vong-quay-may-man';
    pluralName: 'luck-ky-wheel';
    displayName: 'V\u00F2ng quay may m\u1EAFn';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    wheelImage: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    spinMethod: Attribute.Enumeration<
      [
        'Th\u00E0nh vi\u00EAn',
        '\u0110\u01A1n h\u00E0ng th\u00E0nh c\u00F4ng',
        'M\u1EDF t\u00E0i kho\u1EA3n bank',
        'Mua b\u1EA3o hi\u1EC3m'
      ]
    >;
    spinBasedOn: Attribute.Enumeration<
      [
        'Gi\u00E1 tr\u1ECB \u0111\u01A1n h\u00E0ng (\u0111)',
        'Ng\u1EABu nhi\u00EAn'
      ]
    >;
    minNumber: Attribute.Integer;
    maxNumber: Attribute.Integer;
    speed: Attribute.Integer;
    spinsPerDay: Attribute.Integer;
    intervalMinutes: Attribute.Integer;
    startDate: Attribute.Date;
    endDate: Attribute.Date;
    programRules: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    backgroundWheel: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    backgroundMenuHexColor: Attribute.String;
    orderAmount: Attribute.Integer;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::vong-quay-may-man.vong-quay-may-man',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::vong-quay-may-man.vong-quay-may-man',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWebSocialConfigWebSocialConfig
  extends Schema.CollectionType {
  collectionName: 'web_social_configs';
  info: {
    singularName: 'web-social-config';
    pluralName: 'web-social-configs';
    displayName: 'Web social config';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    url: Attribute.String;
    icon: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    type: Attribute.Enumeration<['social', 'appstore']>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::web-social-config.web-social-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::web-social-config.web-social-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWebTrangChuWebTrangChu extends Schema.SingleType {
  collectionName: 'web_trang_chus';
  info: {
    singularName: 'web-trang-chu';
    pluralName: 'web-trang-chus';
    displayName: 'WEB - Trang ch\u1EE7';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    section1_heading1: Attribute.String;
    section1_heading2: Attribute.String;
    section1_text: Attribute.String;
    section1_image_right: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    section2_heading1: Attribute.String;
    section2_heading2: Attribute.String;
    section2_image_center: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
    section3_about_heading1: Attribute.String;
    section3_about_heading2: Attribute.String;
    section3_about_text: Attribute.String;
    section3_about_image_bg: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    section4_feature_heading1: Attribute.String;
    section4_feature_text: Attribute.String;
    section3_about_link_download: Attribute.String;
    section4_about_image_bg: Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    logo_white: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    frontend_footer_email: Attribute.String;
    frontend_footer_phone: Attribute.String;
    frontend_footer_about: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::web-trang-chu.web-trang-chu',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::web-trang-chu.web-trang-chu',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'api::app-config.app-config': ApiAppConfigAppConfig;
      'api::bao-hiem.bao-hiem': ApiBaoHiemBaoHiem;
      'api::car.car': ApiCarCar;
      'api::car-category.car-category': ApiCarCategoryCarCategory;
      'api::car-history.car-history': ApiCarHistoryCarHistory;
      'api::car-type.car-type': ApiCarTypeCarType;
      'api::category-news.category-news': ApiCategoryNewsCategoryNews;
      'api::gift-shop-danh-muc.gift-shop-danh-muc': ApiGiftShopDanhMucGiftShopDanhMuc;
      'api::grid-menu-home.grid-menu-home': ApiGridMenuHomeGridMenuHome;
      'api::insurance-history.insurance-history': ApiInsuranceHistoryInsuranceHistory;
      'api::lich-su-quay.lich-su-quay': ApiLichSuQuayLichSuQuay;
      'api::loai-bao-hiem.loai-bao-hiem': ApiLoaiBaoHiemLoaiBaoHiem;
      'api::loai-chi-phi.loai-chi-phi': ApiLoaiChiPhiLoaiChiPhi;
      'api::loai-xe.loai-xe': ApiLoaiXeLoaiXe;
      'api::news.news': ApiNewsNews;
      'api::pvcombank-log.pvcombank-log': ApiPvcombankLogPvcombankLog;
      'api::shop.shop': ApiShopShop;
      'api::vong-quay-may-man.vong-quay-may-man': ApiVongQuayMayManVongQuayMayMan;
      'api::web-social-config.web-social-config': ApiWebSocialConfigWebSocialConfig;
      'api::web-trang-chu.web-trang-chu': ApiWebTrangChuWebTrangChu;
    }
  }
}
