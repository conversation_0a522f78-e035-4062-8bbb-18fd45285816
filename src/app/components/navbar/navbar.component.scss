.bg-header {
  background-color: var(--color-steelblue-300);
  position: sticky; /* <PERSON><PERSON>m bảo z-index hoạt động */
  z-index: 1000; /* Z-index thấp hơn logo */
}

/* Container cho navbar - điều chỉnh để đảm bảo chiều cao 75px */
.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0; /* Loại bỏ padding để tránh vư<PERSON><PERSON> quá 75px */
  width: 100%;
  height: 75px; /* Đặt chiều cao cố định 75px */
}

/* Logo container - đảm bảo luôn hiển thị trên cùng */
.logo-container {
  flex: 0 0 auto;
  position: absolute;
  top: 27px;
  z-index: 9999;
}

/* Menu chính */
.menu-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1 1 auto;
  padding: 0;
  margin-right: 36px;
}

/* Right menu container */
.right-menu {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 0 0 auto;
}

/* Container cho các icon */
.icons-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Style cho các menu item với hover effect */
.menu {
  position: relative;
  color: var(--color-white);
  cursor: pointer;
  z-index: 1;
  font-weight: 500;
  padding: 0px 30px; /* Thêm padding để tăng vùng click */
  border-radius: 0px; /* Bo góc nhẹ */
  transition: all 0.3s ease; /* Smooth transition */
  height: 75px; /* Đặt chiều cao cố định */
  display: flex;
  align-items: center;
  justify-content: center;

  /* Hover effect */
  &:hover {
    background-color: #5A94C1; /* Màu active theo yêu cầu */
    color: var(--color-white);
  }

  /* Active state khi đang ở trang hiện tại */
  &.active {
    background-color: #073A69;
    color: var(--color-white);
  }
}

.frame-child5 {
  height: 32px;
  width: 161px;
  position: relative;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  display: none;
}
.image-579-icon {
  width: 20px;
  position: relative;
  max-height: 100%;
  object-fit: cover;
  z-index: 2;
}
.t-bn-ngay {
  position: relative;
  font-size: var(--font-size-14);
  font-family: var(--font-varela);
  color: var(--color-white);
  text-align: left;
  z-index: 2;
}
.frame-wrapper3,
.rectangle-parent3 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.rectangle-parent3 {
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  flex-direction: row;
  padding: var(--padding-6) 9px var(--padding-6) var(--padding-14);
  gap: var(--gap-10);
  z-index: 1;
}
.frame-wrapper3 {
  cursor: pointer;
  border: 0;
  padding: var(--padding-5) 9px 0 0;
  background-color: transparent;
  flex-direction: column;
  transition: all 0.3s ease;

  &:hover:not(:disabled) .rectangle-parent3 {
    background-color: var(--color-steelblue-200);
    transform: translateY(-1px);
  }

  &:disabled,
  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;

    .rectangle-parent3 {
      background-color: var(--color-darkgray-200);
      transform: none;
    }

    .t-bn-ngay {
      color: var(--color-darkgray-100);
    }

    &:hover .rectangle-parent3 {
      background-color: var(--color-darkgray-200);
      transform: none;
    }
  }
}
.bell-notification-1-icon {
  width: 24px;
  height: 24px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 1;
}
.bell-notification-1-wrapper,
.shopping-cart-1-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-12) 0 0;
}
.shopping-cart-1-wrapper {
  padding: var(--padding-11) var(--padding-6) 0 0;
}

// Auth links styles
.user-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-11) var(--padding-6) 0 0;

  .auth-links {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    z-index: 1;

    .auth-link {
      position: relative;
      font-size: var(--font-size-14);
      font-family: var(--font-varela);
      color: var(--color-white);
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: var(--color-steelblue-100);
        text-decoration: underline;
      }
    }

    .separator {
      color: var(--color-white);
      font-size: var(--font-size-14);
    }
  }
}
.frame-child6 {
  height: 40px;
  width: 81px;
  position: relative;
  border-radius: var(--br-100);
  border: 1px solid var(--color-steelblue-100);
  box-sizing: border-box;
  display: none;
}
.image-541-icon {
  width: 20px;
  height: 20px;
  position: relative;
  object-fit: cover;
  z-index: 2;
}
.image-541-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.image-533-icon {
  height: 32px;
  width: 32px;
  position: relative;
  object-fit: cover;
  z-index: 2;
}
.cho-hai-san-child,
.frame-header {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
}

.cho-hai-san-child,
.frame-header {
  max-width: 100%;
}
.frame-header {
  flex: 1;
  padding: var(--padding-16) 125px var(--padding-19) 439px;
  gap: var(--gap-14);
  top: 0;
  z-index: 99;
  position: sticky;
  text-align: left;
  font-size: var(--font-size-14);
  color: var(--color-white);
  font-family: var(--font-varela);
}
@media screen and (max-width: 750px) {
  .trang-ch-parent {
    display: none;
  }
  .frame-header {
    padding-left: 219px;
    padding-right: 62px;
    box-sizing: border-box;
  }
}
@media screen and (max-width: 450px) {
  .frame-header {
    padding-left: var(--padding-20);
    padding-right: var(--padding-20);
    box-sizing: border-box;
  }
  .cho-hai-san-child {
    padding-bottom: 211px;
    box-sizing: border-box;
  }
}
.logo-white {
  width: 100px;
  height: 100px;
  object-fit: contain;
  z-index: 99999 !important; /* Z-index cao nhất để logo luôn hiển thị */
  display: block;
  position: relative; /* Đảm bảo z-index hoạt động */
  pointer-events: auto; /* Đảm bảo logo có thể click được */
}

/* Mobile Menu Styles - điều chỉnh để đảm bảo chiều cao 75px */
.mobile-navbar {
  width: 100%;
  height: 75px; /* Đồng nhất với desktop */
}

.mobile-navbar .logo-white {
  width: 60px;
  height: 60px;
}

/* Specific styling for the mobile navigation items */
.mobile-navbar {
  position: relative;
}

.mobile-navbar .logo-container {
  position: absolute;
  left: calc(50% - 30px);
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: center;
  z-index: 0 !important;
  pointer-events: auto;
  width: 60px;
}

.mobile-left,
.mobile-right {
  display: flex;
  align-items: center;
  z-index: 10;
  padding: 0 5px;
}

/* Mobile menu dropdown */
.mobile-menu {
  position: fixed;
  top: 75px; /* Điều chỉnh theo navbar height mới */
  left: 0;
  right: 0;
  background-color: var(--color-steelblue-300);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-150%);
  transition: transform 0.3s ease;
  z-index: 100;
}

.show-mobile-menu {
  transform: translateY(0);
}

.mobile-menu-item {
  padding: 15px 16px; /* Thêm padding horizontal */
  color: var(--color-white);
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease; /* Smooth transition */
  border-radius: 4px; /* Bo góc nhẹ */
  margin: 0 16px; /* Margin để tạo khoảng cách */

  /* Hover effect cho mobile menu */
  &:hover {
    background-color: #073A69; /* Màu active theo yêu cầu */
    color: var(--color-white);
    transform: translateX(4px); /* Hiệu ứng trượt nhẹ */
  }

  /* Active state */
  &.active {
    background-color: #073A69;
    color: var(--color-white);
  }
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

.mobile-booking-btn {
  background-color: var(--color-steelblue-100);
  color: var(--color-white);
  border: none;
  border-radius: var(--br-5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background-color: var(--color-steelblue-200);
    transform: translateY(-1px);
  }

  &:disabled,
  &.disabled {
    background-color: var(--color-darkgray-200);
    color: var(--color-darkgray-100);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;

    &:hover {
      background-color: var(--color-darkgray-200);
      transform: none;
    }
  }
}

.booking-icon {
  width: 20px;
  height: 20px;
}

/* Menu icon styling */
.menu-icon {
  width: 28px;
  height: 28px;
  color: var(--color-white);
  cursor: pointer;
}
.shopping-cart-1-wrapper, .bell-notification-1-wrapper {
  cursor: pointer;
}

/* Cart badge styles */
.cart-icon-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #dc3545;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  border: 2px solid var(--color-steelblue-300);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;

  /* Animation for new items */
  animation: badge-bounce 0.3s ease-in-out;
}

@keyframes badge-bounce {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .cart-badge {
    top: -6px;
    right: -6px;
    font-size: 11px;
    min-width: 16px;
    height: 16px;
    padding: 1px 4px;
  }
}

/* Đảm bảo navbar không vượt quá 75px trên tất cả devices */
@media screen and (max-width: 1200px) {
  .menu-wrapper {
    gap: 24px; /* Giảm gap trên màn hình nhỏ hơn */
  }

  .menu {
    padding: 6px 12px; /* Giảm padding */
    font-size: 13px; /* Giảm font size nhẹ */
  }
}

@media screen and (max-width: 992px) {
  .menu-wrapper {
    gap: 16px;
  }

  .menu {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* Đảm bảo container không có overflow */
.bg-header {
  overflow: hidden;
  max-height: 75px;
}

/* Đảm bảo tất cả elements trong navbar fit trong 75px */
.navbar-container * {
  box-sizing: border-box;
}
/* Đảm bảo logo không bị che bởi sticky elements */
.sticky-top {
  z-index: 1000 !important;
}

/* Override z-index cho các elements có thể che logo */
.frame-header {
  z-index: 99 !important; /* Thấp hơn logo */
}

/* Đảm bảo logo container có thể thò ra ngoài navbar */
.bg-header {
  overflow: visible !important; /* Cho phép logo thò ra */
}
