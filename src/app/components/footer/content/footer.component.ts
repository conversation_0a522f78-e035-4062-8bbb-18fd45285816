import { Component, ViewEncapsulation, HostBinding, Input, OnInit } from '@angular/core';

import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FooterService } from '../../../core/services/footer.service';
@Component({
  selector: 'app-footer-content',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule, RouterModule],
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterContentComponent implements OnInit {
  @HostBinding('style.display') display = 'contents';

  // Dữ liệu từ API
  socialLinks: any[] = [];
  appStoreLinks: any[] = [];
  isLoading = true;

  constructor(private footerService: FooterService) {}

  ngOnInit() {
    this.loadFooterData();
  }

  /** Value props */
  @Input() image616: string = '';
  /** Style props */
  @Input() frameFooterPadding: string | number = '';
  @Input() rectangleDivHeight: string | number = '';

  /**
   * Load dữ liệu footer từ API
   */
  private loadFooterData(): void {
    this.footerService.getProcessedFooterData().subscribe({
      next: data => {
        this.socialLinks = data.socialLinks;
        this.appStoreLinks = data.appStoreLinks;
        this.isLoading = false;
      },
      error: error => {
        console.error('Lỗi khi tải dữ liệu footer:', error);
        this.isLoading = false;
        // Fallback to default data nếu API lỗi
        this.setDefaultData();
      },
    });
  }

  /**
   * Set dữ liệu mặc định khi API lỗi
   */
  private setDefaultData(): void {
    this.socialLinks = [
      { name: 'Facebook', url: '#', iconUrl: 'assets/<EMAIL>' },
      { name: 'TikTok', url: '#', iconUrl: 'assets/<EMAIL>' },
      { name: 'YouTube', url: '#', iconUrl: 'assets/<EMAIL>' },
    ];

    this.appStoreLinks = [
      { name: 'App Store', url: '#', iconUrl: 'assets/<EMAIL>' },
      { name: 'Google Play', url: '#', iconUrl: 'assets/<EMAIL>' },
    ];
  }

  get frameFooterStyle() {
    return {
      padding: this.frameFooterPadding,
    };
  }

  get rectangleDiv3Style() {
    return {
      height: this.rectangleDivHeight,
    };
  }
}
