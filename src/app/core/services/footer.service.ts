import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

export interface SocialConfig {
  id: number;
  attributes: {
    name: string;
    url: string;
    type: 'social' | 'appstore';
    icon?: {
      data?: {
        attributes: {
          url: string;
        };
      };
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
}

export interface FooterData {
  data: SocialConfig[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface ProcessedFooterData {
  socialLinks: {
    name: string;
    url: string;
    iconUrl?: string;
  }[];
  appStoreLinks: {
    name: string;
    url: string;
    iconUrl?: string;
  }[];
}

@Injectable({
  providedIn: 'root',
})
export class FooterService {
  private readonly API_BASE_URL = environment.apiUrlV2;
  private readonly API_ENDPOINT = 'api/web-social-configs';
  private readonly API_WEB_CONFIG = 'api/web-trang-chu';

  constructor(private http: HttpClient) {}

  /**
   * Lấy dữ liệu footer từ API
   * @returns Observable<FooterData>
   */
  getFooterData(): Observable<FooterData> {
    const url = `${this.API_BASE_URL}/${this.API_ENDPOINT}?populate=*`;

    return this.http.get<FooterData>(url);
  }

  /**
   * Lấy và xử lý dữ liệu footer
   * @returns Observable<ProcessedFooterData>
   */
  getProcessedFooterData(): Observable<ProcessedFooterData> {
    return this.getFooterData().pipe(
      map((response: FooterData) => {
        const socialLinks: any[] = [];
        const appStoreLinks: any[] = [];

        if (response.data && Array.isArray(response.data)) {
          response.data.forEach(item => {
            const config = {
              name: item.attributes.name,
              url: item.attributes.url,
              iconUrl: item.attributes.icon?.data?.attributes?.url || '',
            };

            if (item.attributes.type === 'social') {
              socialLinks.push(config);
            } else if (item.attributes.type === 'appstore') {
              appStoreLinks.push(config);
            }
          });
        }

        return {
          socialLinks,
          appStoreLinks,
        };
      }),
    );
  }

  /**
   * Lấy danh sách social links
   * @returns Observable<any[]>
   */
  getSocialLinks(): Observable<any[]> {
    return this.getProcessedFooterData().pipe(map(data => data.socialLinks));
  }

  /**
   * Lấy danh sách app store links
   * @returns Observable<any[]>
   */
  getAppStoreLinks(): Observable<any[]> {
    return this.getProcessedFooterData().pipe(map(data => data.appStoreLinks));
  }
}
